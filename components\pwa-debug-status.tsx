"use client"

import { useState, useEffect } from "react"
import { CheckCircle, XCircle, AlertCircle, Wifi, Shield, Smartphone } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface PWAStatus {
  isHTTPS: boolean
  hasManifest: boolean
  hasServiceWorker: boolean
  manifestValid: boolean
  serviceWorkerRegistered: boolean
  isInstallable: boolean
  isStandalone: boolean
  browserSupport: boolean
}

export function PWADebugStatus() {
  const [status, setStatus] = useState<PWAStatus>({
    isHTTPS: false,
    hasManifest: false,
    hasServiceWorker: false,
    manifestValid: false,
    serviceWorkerRegistered: false,
    isInstallable: false,
    isStandalone: false,
    browserSupport: false
  })
  const [manifestData, setManifestData] = useState<any>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    checkPWAStatus()
  }, [])

  const checkPWAStatus = async () => {
    const newStatus: PWAStatus = {
      isHTTPS: window.location.protocol === 'https:' || window.location.hostname === 'localhost',
      hasManifest: false,
      hasServiceWorker: false,
      manifestValid: false,
      serviceWorkerRegistered: false,
      isInstallable: false,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches || (navigator as any).standalone === true,
      browserSupport: 'serviceWorker' in navigator && 'BeforeInstallPromptEvent' in window
    }

    // Check manifest
    try {
      const manifestResponse = await fetch('/manifest.json')
      if (manifestResponse.ok) {
        newStatus.hasManifest = true
        const manifest = await manifestResponse.json()
        setManifestData(manifest)
        
        // Basic manifest validation
        newStatus.manifestValid = !!(
          manifest.name &&
          manifest.short_name &&
          manifest.start_url &&
          manifest.display &&
          manifest.icons &&
          manifest.icons.length > 0
        )
      }
    } catch (error) {
      console.error('Manifest check failed:', error)
    }

    // Check service worker
    if ('serviceWorker' in navigator) {
      newStatus.hasServiceWorker = true
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        newStatus.serviceWorkerRegistered = !!registration
      } catch (error) {
        console.error('Service worker check failed:', error)
      }
    }

    // Check if installable (this is tricky to detect reliably)
    newStatus.isInstallable = newStatus.isHTTPS && 
                              newStatus.hasManifest && 
                              newStatus.manifestValid && 
                              newStatus.serviceWorkerRegistered &&
                              newStatus.browserSupport

    setStatus(newStatus)
  }

  const StatusIcon = ({ condition }: { condition: boolean }) => {
    return condition ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }

  const StatusBadge = ({ condition, trueText, falseText }: { 
    condition: boolean, 
    trueText: string, 
    falseText: string 
  }) => {
    return (
      <Badge variant={condition ? "default" : "destructive"}>
        {condition ? trueText : falseText}
      </Badge>
    )
  }

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="outline"
        size="sm"
        className="fixed top-4 right-4 z-50"
      >
        PWA Debug
      </Button>
    )
  }

  return (
    <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96 max-h-[80vh] overflow-y-auto">
      <Card className="shadow-lg border-2 border-purple-200 bg-white">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-5 w-5 text-purple-600" />
              <CardTitle className="text-lg">PWA Status Debug</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
          <CardDescription>
            Check PWA installation requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {/* HTTPS Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span className="text-sm">HTTPS/Secure Context</span>
            </div>
            <StatusBadge 
              condition={status.isHTTPS} 
              trueText="Secure" 
              falseText="Not Secure" 
            />
          </div>

          {/* Manifest Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon condition={status.hasManifest} />
              <span className="text-sm">Manifest Available</span>
            </div>
            <StatusBadge 
              condition={status.hasManifest} 
              trueText="Found" 
              falseText="Missing" 
            />
          </div>

          {/* Manifest Valid */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon condition={status.manifestValid} />
              <span className="text-sm">Manifest Valid</span>
            </div>
            <StatusBadge 
              condition={status.manifestValid} 
              trueText="Valid" 
              falseText="Invalid" 
            />
          </div>

          {/* Service Worker */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon condition={status.serviceWorkerRegistered} />
              <span className="text-sm">Service Worker</span>
            </div>
            <StatusBadge 
              condition={status.serviceWorkerRegistered} 
              trueText="Registered" 
              falseText="Not Registered" 
            />
          </div>

          {/* Browser Support */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon condition={status.browserSupport} />
              <span className="text-sm">Browser Support</span>
            </div>
            <StatusBadge 
              condition={status.browserSupport} 
              trueText="Supported" 
              falseText="Not Supported" 
            />
          </div>

          {/* Overall Installable Status */}
          <div className="border-t pt-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <StatusIcon condition={status.isInstallable} />
                <span className="text-sm font-medium">PWA Installable</span>
              </div>
              <StatusBadge 
                condition={status.isInstallable} 
                trueText="Ready" 
                falseText="Not Ready" 
              />
            </div>
          </div>

          {/* Current Status */}
          <div className="text-xs text-gray-600 space-y-1">
            <div>Protocol: {window.location.protocol}</div>
            <div>Host: {window.location.host}</div>
            <div>Standalone: {status.isStandalone ? 'Yes' : 'No'}</div>
            <div>User Agent: {navigator.userAgent.substring(0, 40)}...</div>
          </div>

          {/* Refresh Button */}
          <Button 
            onClick={checkPWAStatus} 
            variant="outline" 
            size="sm" 
            className="w-full"
          >
            Refresh Status
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
