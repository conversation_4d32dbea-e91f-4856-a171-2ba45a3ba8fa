
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ***********
IP.3 = ***********00
IP.4 = ***********01
IP.5 = ***********02
IP.6 = ***********03
IP.7 = ***********04
IP.8 = ***********05
IP.9 = ********
IP.10 = **********
