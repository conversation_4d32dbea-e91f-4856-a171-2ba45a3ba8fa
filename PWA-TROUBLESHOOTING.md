# PWA Install Prompt Troubleshooting Guide

## 🔍 Quick Diagnosis

1. **Open your app on mobile**: `http://[your-ip]:3000`
2. **Look for the "PWA Debug" button** in the top-right corner
3. **Click it to see detailed status** of all PWA requirements
4. **Check browser console** for debug messages from InstallPrompt component

## 🚨 Most Common Issue: HTTPS Required

**Problem**: PWA install prompts typically require HTTPS when accessing via network IP addresses.

**Solution**: Set up HTTPS for local development:

### Option 1: Using mkcert (Recommended)

```bash
# Install mkcert
# Windows: choco install mkcert
# macOS: brew install mkcert  
# Linux: apt install mkcert

# Install local CA
mkcert -install

# Generate certificates for your local network
mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 192.168.1.* 10.0.0.*

# Run HTTPS development server
npm run dev:https
```

### Option 2: Self-signed Certificate

```bash
# Create certs directory
mkdir certs

# Generate self-signed certificate
openssl req -x509 -newkey rsa:2048 -keyout certs/localhost-key.pem -out certs/localhost.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Run with custom HTTPS server
node server-https.js
```

## 📱 Browser-Specific Requirements

### Android Chrome
- ✅ Requires HTTPS (except localhost)
- ✅ Must meet PWA criteria (manifest + service worker)
- ✅ User engagement heuristics may apply
- ✅ May need to wait 30+ seconds for prompt to appear

### Android Brave
- ✅ Similar to Chrome requirements
- ✅ May have stricter security requirements
- ✅ Check if "Block scripts" is disabled

### iOS Safari
- ✅ Uses "Add to Home Screen" instead of install prompt
- ✅ No beforeinstallprompt event
- ✅ Manual installation via Share button

## 🔧 Debugging Steps

### 1. Check PWA Debug Status
- Open app and click "PWA Debug" button
- Verify all items show green checkmarks
- Pay attention to HTTPS status

### 2. Browser Developer Tools
```javascript
// Open mobile browser dev tools and run:
console.log('HTTPS:', location.protocol === 'https:');
console.log('Service Worker:', 'serviceWorker' in navigator);
console.log('Manifest:', document.querySelector('link[rel="manifest"]'));

// Check if beforeinstallprompt is supported
console.log('Install prompt support:', 'BeforeInstallPromptEvent' in window);
```

### 3. Network Accessibility Test
```bash
# Test manifest accessibility
curl http://[your-ip]:3000/manifest.json

# Test service worker accessibility  
curl http://[your-ip]:3000/sw.js
```

### 4. Service Worker Registration
```javascript
// Check service worker status
navigator.serviceWorker.getRegistrations().then(registrations => {
  console.log('SW Registrations:', registrations.length);
  registrations.forEach(reg => console.log('SW:', reg.scope));
});
```

## ⚡ Quick Fixes

### Fix 1: Force HTTPS
```bash
# Use HTTPS development server
npm run dev:https
# Then access: https://[your-ip]:3000
```

### Fix 2: Clear Browser Cache
1. Open Chrome DevTools
2. Go to Application tab
3. Click "Clear storage"
4. Refresh page

### Fix 3: Wait for Engagement
- Some browsers require user interaction
- Navigate around the app for 30+ seconds
- Try refreshing the page

### Fix 4: Check Manifest Validation
- Open Chrome DevTools
- Go to Application > Manifest
- Look for validation errors

## 🎯 Testing Checklist

- [ ] HTTPS enabled for network access
- [ ] Manifest.json accessible and valid
- [ ] Service worker registered successfully
- [ ] No console errors
- [ ] PWA Debug Status shows all green
- [ ] Waited 30+ seconds for prompt
- [ ] Tried refreshing the page
- [ ] Tested on multiple browsers

## 📋 Expected Behavior

### When Working Correctly:
1. **Debug info appears** in development mode
2. **PWA Debug Status** shows all requirements met
3. **Install prompt appears** within 30 seconds
4. **Console shows** "beforeinstallprompt event fired!"

### iOS Behavior:
- No automatic install prompt
- Use Safari Share button → "Add to Home Screen"
- App will work as PWA after manual installation

## 🆘 Still Not Working?

1. **Check the PWA Debug Status component** - it will show exactly what's missing
2. **Look at browser console** for specific error messages
3. **Try HTTPS setup** - this fixes 90% of network access issues
4. **Test on localhost first** to verify PWA works locally
5. **Check your local IP** - make sure you're using the correct network address

## 📞 Common Error Messages

| Error | Solution |
|-------|----------|
| "beforeinstallprompt event did not fire" | Enable HTTPS or check PWA criteria |
| "Manifest not found" | Verify manifest.json is accessible |
| "Service worker registration failed" | Check sw.js accessibility and syntax |
| "Not secure context" | Use HTTPS for network access |
| "Browser not supported" | Use Chrome/Edge/Firefox for testing |
