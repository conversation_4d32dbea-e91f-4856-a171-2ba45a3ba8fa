"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { ArrowLeft, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ProtectedLayout } from "@/components/protected-layout"
import { DiseaseBadgeSelector } from "@/components/disease-badge-selector"
import { dataClient } from "@/lib/data-client"
import { db } from "@/lib/db"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import { useOnlineStatus } from "@/hooks/use-online-status"
import { useUnitTypes } from "@/hooks/use-unit-types"
import { usePatientIdValidation } from "@/hooks/use-patient-data"
import { toastSuccess, toastError } from "@/lib/toast-utils"
import type { UnitType } from "@/lib/types"
import { GENDER_TYPES } from "@/lib/constants"
import type { GenderType } from "@/lib/constants"

const patientSchema = z.object({
  patient_id: z.string().min(1, "Patient ID is required"),
  name: z.string().min(1, "Full name is required"),
  admission_date: z.string().min(1, "Admission date is required"),
  gender: z.string().min(1, "Gender is required"),
  age: z.number().min(1).max(150),
  weight: z.number().positive(),
  unit_id: z.number().min(1, "Unit is required"),
  main_complaint: z.string().min(1, "Main complaint is required"),
  medical_history: z.string().optional(),
  initial_diagnosis: z.string().min(1, "Initial diagnosis is required"),
})

type PatientFormData = z.infer<typeof patientSchema>

// Generate random 8-digit number
function generateRandomId(): string {
  return Math.floor(10000000 + Math.random() * 90000000).toString()
}

export default function AddPatientPage() {
  const [selectedDiseases, setSelectedDiseases] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const genderTypes = GENDER_TYPES
  const router = useRouter()
  const { toast } = useToast()
  const isOnline = useOnlineStatus()

  // Use consolidated hooks
  const { unitTypes } = useUnitTypes()
  const { validatePatientId, isValidating: isValidatingId, error: patientIdError, clearError } = usePatientIdValidation()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      admission_date: new Date().toISOString().split("T")[0],
      patient_id: generateRandomId(),
    },
  })

  const patientIdValue = watch("patient_id")

  // Generate new random ID
  const generateNewId = () => {
    const newId = generateRandomId()
    setValue("patient_id", newId)
    clearError()
  }

  // Unit types are now loaded by the useUnitTypes hook

  // Debounced validation effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (patientIdValue) {
        validatePatientId(patientIdValue)
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [patientIdValue])

  const onSubmit = async (data: PatientFormData) => {
    // Final validation before submission
    if (patientIdError) {
      toastError.patient.idExists()
      return
    }

    setIsSubmitting(true)

    try {
      // Double-check uniqueness before submission
      const isValid = await validatePatientId(data.patient_id)
      if (!isValid) {
        setIsSubmitting(false)
        return
      }

      await dataClient.insertPatient({
        ...data,
        gender: data.gender as GenderType,
        diseases: selectedDiseases,
      })

      toastSuccess.patient.added()
      router.push("/")
    } catch (error) {
      toastError.patient.addFailed()
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <ProtectedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <h1 className="text-3xl font-bold">Add Patient</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Patient Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input id="name" placeholder="patient's full name" {...register("name")} />
                  {errors.name && <p className="text-sm text-red-600">{errors.name.message}</p>}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="patient_id">Patient ID *</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={generateNewId}
                      className="h-6 px-2 text-xs"
                      title="Generate new random ID"
                    >
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="relative">
                    <Input 
                      id="patient_id" 
                      placeholder="Auto-generated 8-digit ID (editable)" 
                      {...register("patient_id")}
                      className={patientIdError ? "border-red-500" : ""}
                    />
                    {isValidatingId && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                      </div>
                    )}
                  </div>
                  {patientIdError && <p className="text-sm text-red-600">{patientIdError}</p>}
                  {errors.patient_id && <p className="text-sm text-red-600">{errors.patient_id.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="admission_date">Admission Date</Label>
                  <Input id="admission_date" type="date" {...register("admission_date")} />
                  {errors.admission_date && <p className="text-sm text-red-600">{errors.admission_date.message}</p>}
                </div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
               
              <div className="space-y-2">
                  <Label htmlFor="unit">Unit *</Label>
                  <Controller
                    name="unit_id"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitTypes.length === 0 ? (
                            <SelectItem value="loading" disabled>Loading units...</SelectItem>
                          ) : (
                            unitTypes.map((unit) => (
                              <SelectItem key={unit.id} value={unit.id.toString()}>
                                {unit.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.unit_id && <p className="text-sm text-red-600">{errors.unit_id.message}</p>}
                </div>
               
                <div className="space-y-2">
                  <Label htmlFor="gender">Gender *</Label>
                  <Select onValueChange={(value) => setValue("gender", value as GenderType)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      {genderTypes.map((gender) => (
                        <SelectItem key={gender} value={gender}>
                          {gender}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.gender && <p className="text-sm text-red-600">{errors.gender.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="age">Age *</Label>
                  <Input id="age" type="number" placeholder="age" {...register("age", { valueAsNumber: true })} />
                  {errors.age && <p className="text-sm text-red-600">{errors.age.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg) *</Label>
                  <Input
                    id="weight"
                    type="number"
                    step="0.1"
                    placeholder="weight"
                    {...register("weight", { valueAsNumber: true })}
                  />
                  {errors.weight && <p className="text-sm text-red-600">{errors.weight.message}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="main_complaint">Main Complaint *</Label>
                <Textarea id="main_complaint" placeholder="Describe main complaint" {...register("main_complaint")} />
                {errors.main_complaint && <p className="text-sm text-red-600">{errors.main_complaint.message}</p>}
              </div>

              <div className="space-y-2">
                <Label> Disease History</Label>
                <DiseaseBadgeSelector selectedDiseases={selectedDiseases} onChange={setSelectedDiseases} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="medical_history">Additional Medical History</Label>
                <Textarea
                  id="medical_history"
                  placeholder="Additional medical history, medications, allergies, etc."
                  {...register("medical_history")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="initial_diagnosis">Initial Diagnosis *</Label>
                <Textarea id="initial_diagnosis" placeholder="initial diagnosis" {...register("initial_diagnosis")} />
                {errors.initial_diagnosis && <p className="text-sm text-red-600">{errors.initial_diagnosis.message}</p>}
              </div>

              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Adding Patient..." : "Add Patient"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
