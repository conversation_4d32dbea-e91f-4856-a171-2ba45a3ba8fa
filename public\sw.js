const CACHE_NAME = "icu-patient-management-v3"
const OFFLINE_FALLBACK = "/offline.html"
const APP_SHELL = [
  "/",
  "/login",
  "/add-patient",
  "/admin",
  "/manifest.json",
  OFFLINE_FALLBACK
]

self.addEventListener("install", (event) => {
  event.waitUntil(
    (async () => {
      const cache = await caches.open(CACHE_NAME)
      await cache.addAll(APP_SHELL)
      await self.skipWaiting()
    })(),
  )
})

function cacheThenNetwork(event) {
  event.respondWith(
    caches.match(event.request).then((cached) => {
      const fetchPromise = fetch(event.request)
        .then((response) => {
          const respClone = response.clone()
          caches.open(CACHE_NAME).then((cache) => cache.put(event.request, respClone))
          return response
        })
        .catch(() => cached)
      return cached || fetchPromise
    }),
  )
}

self.addEventListener("fetch", (event) => {
  const { request } = event

  // Ignore cross-origin requests; let the browser handle them
  // so connectivity checks are accurate and not masked by SW
  try {
    const extUrl = new URL(request.url)
    if (extUrl.origin !== self.location.origin) {
      return
    }
  } catch (e) {
    // Non-HTTP(s) requests
    return
  }

  if (request.mode === "navigate") {
    event.respondWith(
      (async () => {
        try {
          const networkResponse = await fetch(request)
          const cache = await caches.open(CACHE_NAME)
          cache.put(request, networkResponse.clone())
          return networkResponse
        } catch {
          const cache = await caches.open(CACHE_NAME)
          const cachedPage = await cache.match(request)

          if (cachedPage) {
            return cachedPage
          }

          // Try to return the main page
          const mainPage = await cache.match("/")
          if (mainPage) {
            return mainPage
          }

          // If nothing is cached, return offline fallback
          return await cache.match(OFFLINE_FALLBACK)
        }
      })(),
    )
    return
  }

  const url = new URL(request.url)

  if (url.origin === self.location.origin) {
    if (url.pathname.startsWith("/_next/")) {
      cacheThenNetwork(event)
      return
    }

    if (/(\.js|\.css|\.png|\.jpg|\.jpeg|\.gif|\.svg|\.webp|\.ico|\.woff2?|\.ttf|\.eot)$/i.test(url.pathname)) {
      cacheThenNetwork(event)
      return
    }
  }

  event.respondWith(
    fetch(request)
      .then((response) => {
        const respClone = response.clone()
        caches.open(CACHE_NAME).then((cache) => cache.put(request, respClone))
        return response
      })
      .catch(async () => {
        const cached = await caches.match(request)
        if (cached) {
          return cached
        }

        // For HTML requests, return offline fallback
        if (request.headers.get('accept')?.includes('text/html')) {
          return await caches.match(OFFLINE_FALLBACK)
        }

        // For other requests, just fail
        return new Response('Offline', { status: 503, statusText: 'Service Unavailable' })
      }),
  )
})

self.addEventListener("activate", (event) => {
  event.waitUntil(
    (async () => {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map((name) => {
          if (name !== CACHE_NAME) {
            return caches.delete(name)
          }
        }),
      )
      await self.clients.claim()
    })(),
  )
})
