const CACHE_NAME = "icu-patient-management-v4"
const OFFLINE_FALLBACK = "/offline.html"
const APP_SHELL = [
  "/",
  "/login",
  "/add-patient",
  "/admin",
  "/manifest.json",
  OFFLINE_FALLBACK
]

// Debug logging for network access
function debugLog(message) {
  console.log(`[SW Debug] ${message}`)
}

self.addEventListener("install", (event) => {
  debugLog(`Installing service worker - Cache: ${CACHE_NAME}`)
  debugLog(`Origin: ${self.location.origin}`)

  event.waitUntil(
    (async () => {
      try {
        const cache = await caches.open(CACHE_NAME)
        debugLog(`Cache opened: ${CACHE_NAME}`)

        await cache.addAll(APP_SHELL)
        debugLog(`App shell cached: ${APP_SHELL.length} files`)

        await self.skipWaiting()
        debugLog("Service worker installed and activated")
      } catch (error) {
        debugLog(`Installation error: ${error.message}`)
        throw error
      }
    })(),
  )
})

function cacheThenNetwork(event) {
  event.respondWith(
    caches.match(event.request).then((cached) => {
      const fetchPromise = fetch(event.request)
        .then((response) => {
          const respClone = response.clone()
          caches.open(CACHE_NAME).then((cache) => cache.put(event.request, respClone))
          return response
        })
        .catch(() => cached)
      return cached || fetchPromise
    }),
  )
}

self.addEventListener("fetch", (event) => {
  const { request } = event

  // Ignore cross-origin requests; let the browser handle them
  // so connectivity checks are accurate and not masked by SW
  try {
    const extUrl = new URL(request.url)
    if (extUrl.origin !== self.location.origin) {
      return
    }
  } catch (e) {
    // Non-HTTP(s) requests
    return
  }

  if (request.mode === "navigate") {
    event.respondWith(
      (async () => {
        try {
          const networkResponse = await fetch(request)
          const cache = await caches.open(CACHE_NAME)
          cache.put(request, networkResponse.clone())
          return networkResponse
        } catch {
          const cache = await caches.open(CACHE_NAME)
          const cachedPage = await cache.match(request)

          if (cachedPage) {
            return cachedPage
          }

          // Try to return the main page
          const mainPage = await cache.match("/")
          if (mainPage) {
            return mainPage
          }

          // If nothing is cached, return offline fallback
          return await cache.match(OFFLINE_FALLBACK)
        }
      })(),
    )
    return
  }

  const url = new URL(request.url)

  if (url.origin === self.location.origin) {
    if (url.pathname.startsWith("/_next/")) {
      cacheThenNetwork(event)
      return
    }

    if (/(\.js|\.css|\.png|\.jpg|\.jpeg|\.gif|\.svg|\.webp|\.ico|\.woff2?|\.ttf|\.eot)$/i.test(url.pathname)) {
      cacheThenNetwork(event)
      return
    }
  }

  event.respondWith(
    fetch(request)
      .then((response) => {
        const respClone = response.clone()
        caches.open(CACHE_NAME).then((cache) => cache.put(request, respClone))
        return response
      })
      .catch(async () => {
        const cached = await caches.match(request)
        if (cached) {
          return cached
        }

        // For HTML requests, return offline fallback
        if (request.headers.get('accept')?.includes('text/html')) {
          return await caches.match(OFFLINE_FALLBACK)
        }

        // For other requests, just fail
        return new Response('Offline', { status: 503, statusText: 'Service Unavailable' })
      }),
  )
})

self.addEventListener("activate", (event) => {
  debugLog("Activating service worker")

  event.waitUntil(
    (async () => {
      try {
        const cacheNames = await caches.keys()
        debugLog(`Found ${cacheNames.length} existing caches`)

        await Promise.all(
          cacheNames.map((name) => {
            if (name !== CACHE_NAME) {
              debugLog(`Deleting old cache: ${name}`)
              return caches.delete(name)
            }
          }),
        )

        await self.clients.claim()
        debugLog("Service worker activated and claimed clients")

        // Notify clients that SW is ready
        const clients = await self.clients.matchAll()
        clients.forEach(client => {
          client.postMessage({
            type: 'SW_ACTIVATED',
            cacheName: CACHE_NAME
          })
        })

      } catch (error) {
        debugLog(`Activation error: ${error.message}`)
        throw error
      }
    })(),
  )
})
