const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return '*************'; // fallback
}

const localIP = getLocalIP();

console.log('🔍 Network Accessibility Test for PWA Files');
console.log(`📍 Local IP: ${localIP}`);
console.log('');

// Test URLs to check
const testUrls = [
  `http://localhost:3000/sw.js`,
  `http://localhost:3000/manifest.json`,
  `http://${localIP}:3000/sw.js`,
  `http://${localIP}:3000/manifest.json`,
  `https://localhost:3000/sw.js`,
  `https://localhost:3000/manifest.json`,
  `https://${localIP}:3000/sw.js`,
  `https://${localIP}:3000/manifest.json`
];

async function testUrl(url) {
  return new Promise((resolve) => {
    const isHttps = url.startsWith('https:');
    const client = isHttps ? https : http;
    
    const options = {
      method: 'HEAD',
      timeout: 5000
    };
    
    if (isHttps) {
      options.rejectUnauthorized = false; // Accept self-signed certificates
    }
    
    const req = client.request(url, options, (res) => {
      resolve({
        url,
        status: res.statusCode,
        success: res.statusCode >= 200 && res.statusCode < 300,
        headers: {
          'content-type': res.headers['content-type'],
          'content-length': res.headers['content-length']
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({
        url,
        status: 0,
        success: false,
        error: error.message
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        url,
        status: 0,
        success: false,
        error: 'Timeout'
      });
    });
    
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing file accessibility...');
  console.log('');
  
  for (const url of testUrls) {
    const result = await testUrl(url);
    
    const status = result.success ? '✅' : '❌';
    const statusCode = result.status || 'ERR';
    const error = result.error ? ` (${result.error})` : '';
    
    console.log(`${status} ${statusCode} - ${url}${error}`);
    
    if (result.success && result.headers) {
      console.log(`    Content-Type: ${result.headers['content-type'] || 'unknown'}`);
      console.log(`    Content-Length: ${result.headers['content-length'] || 'unknown'}`);
    }
    console.log('');
  }
  
  console.log('📋 Analysis:');
  console.log('');
  
  // Check if files exist locally
  const swExists = fs.existsSync(path.join(__dirname, '..', 'public', 'sw.js'));
  const manifestExists = fs.existsSync(path.join(__dirname, '..', 'public', 'manifest.json'));
  
  console.log(`📄 sw.js exists locally: ${swExists ? '✅' : '❌'}`);
  console.log(`📄 manifest.json exists locally: ${manifestExists ? '✅' : '❌'}`);
  console.log('');
  
  // Recommendations
  console.log('💡 Recommendations:');
  console.log('');
  
  const httpLocalhost = testUrls.find(url => url.includes('localhost:3000/sw.js') && url.startsWith('http:'));
  const httpNetwork = testUrls.find(url => url.includes(`${localIP}:3000/sw.js`) && url.startsWith('http:'));
  const httpsNetwork = testUrls.find(url => url.includes(`${localIP}:3000/sw.js`) && url.startsWith('https:'));
  
  if (!swExists) {
    console.log('❌ Service worker file missing - check public/sw.js');
  } else if (httpLocalhost && !httpNetwork) {
    console.log('⚠️  Files accessible on localhost but not network IP');
    console.log('   - Check if development server is running with -H 0.0.0.0');
    console.log('   - Verify firewall settings');
  } else if (httpNetwork && !httpsNetwork) {
    console.log('⚠️  Files accessible via HTTP but not HTTPS');
    console.log('   - PWA requires HTTPS for network access');
    console.log('   - Run: npm run dev:https');
  } else if (httpsNetwork) {
    console.log('✅ Files accessible via HTTPS - PWA should work!');
  } else {
    console.log('❌ Files not accessible - check server configuration');
  }
  
  console.log('');
  console.log('🔧 Next steps:');
  console.log('1. Ensure development server is running');
  console.log('2. Use HTTPS for network access: npm run dev:https');
  console.log('3. Check mobile browser console for SW registration errors');
  console.log('4. Use SW Debug component in the app for detailed diagnostics');
}

// Run the tests
runTests().catch(console.error);
