"use client"

import { useState, useEffect } from "react"
import { dataClient } from "@/lib/data-client"
import { useOnlineStatus } from "@/hooks/use-online-status"
import type { UnitType } from "@/lib/types"

/**
 * Custom hook for loading unit types with online/offline strategy
 * Consolidates the duplicate unit loading logic from multiple components
 */
export function useUnitTypes(): {
  unitTypes: UnitType[]
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
} {
  const [unitTypes, setUnitTypes] = useState<UnitType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const isOnline = useOnlineStatus()

  const loadUnitTypes = async (): Promise<void> => {
    try {
      setIsLoading(true)
      setError(null)
      console.log('Loading units, online status:', isOnline)
      
      const unitTypesData = await dataClient.getUnitTypes(isOnline)
      console.log('Loaded unit types:', unitTypesData)
      setUnitTypes(unitTypesData)
    } catch (error) {
      console.error('Error loading unit types:', error)
      
      // Fallback: try to get from local DB only
      try {
        const fallbackUnits = await dataClient.getUnitTypes(false)
        setUnitTypes(fallbackUnits)
        setError(null) // Clear error if fallback succeeds
      } catch (fallbackError) {
        console.error('Error loading fallback units:', fallbackError)
        setError('Failed to load unit types')
      }
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadUnitTypes()
  }, [isOnline])

  return {
    unitTypes,
    isLoading,
    error,
    refetch: loadUnitTypes,
  }
}
