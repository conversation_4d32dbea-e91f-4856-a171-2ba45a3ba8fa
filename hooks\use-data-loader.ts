"use client"

import { useState, useEffect, useCallback } from "react"

interface UseDataLoaderOptions<T> {
  loadFn: () => Promise<T>
  dependencies?: any[]
  initialData?: T | null
  onError?: (error: Error) => void
  onSuccess?: (data: T) => void
}

interface UseDataLoaderResult<T> {
  data: T | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  setData: (data: T | null) => void
}

/**
 * Generic hook for handling async data loading with consistent error handling
 * Consolidates common async patterns used across the application
 */
export function useDataLoader<T>({
  loadFn,
  dependencies = [],
  initialData = null,
  onError,
  onSuccess,
}: UseDataLoaderOptions<T>): UseDataLoaderResult<T> {
  const [data, setData] = useState<T | null>(initialData)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadData = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await loadFn()
      setData(result)
      
      if (onSuccess) {
        onSuccess(result)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      console.error("Data loading error:", err)
      setError(errorMessage)
      
      if (onError && err instanceof Error) {
        onError(err)
      }
    } finally {
      setIsLoading(false)
    }
  }, [loadFn, onError, onSuccess])

  useEffect(() => {
    loadData()
  }, [loadData, ...dependencies])

  return {
    data,
    isLoading,
    error,
    refetch: loadData,
    setData,
  }
}

/**
 * Hook for handling async operations with loading states (like form submissions)
 */
export function useAsyncOperation() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await operation()
      
      if (onSuccess) {
        onSuccess(result)
      }
      
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Operation failed'
      console.error("Async operation error:", err)
      setError(errorMessage)
      
      if (onError && err instanceof Error) {
        onError(err)
      }
      
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    execute,
    isLoading,
    error,
    clearError: () => setError(null),
  }
}

/**
 * Hook for debounced async operations (useful for search, validation, etc.)
 */
export function useDebouncedAsyncOperation(delay: number = 500) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ): Promise<void> => {
    return new Promise((resolve) => {
      const timeoutId = setTimeout(async () => {
        try {
          setIsLoading(true)
          setError(null)
          
          const result = await operation()
          
          if (onSuccess) {
            onSuccess(result)
          }
          
          resolve()
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Operation failed'
          console.error("Debounced async operation error:", err)
          setError(errorMessage)
          
          if (onError && err instanceof Error) {
            onError(err)
          }
          
          resolve()
        } finally {
          setIsLoading(false)
        }
      }, delay)

      // Return cleanup function
      return () => clearTimeout(timeoutId)
    })
  }, [delay])

  return {
    execute,
    isLoading,
    error,
    clearError: () => setError(null),
  }
}
