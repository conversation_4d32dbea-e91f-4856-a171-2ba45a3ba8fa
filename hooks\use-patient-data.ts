"use client"

import { useState, useEffect, useCallback } from "react"
import { dataClient } from "@/lib/data-client"
import type { Patient } from "@/lib/types"

type PatientStatus = "active" | "discharged" | "deceased"

/**
 * Custom hook for loading and managing patient data
 * Consolidates patient loading logic from multiple components
 */
export function usePatientData(status: PatientStatus = "active") {
  const [patients, setPatients] = useState<Patient[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadPatients = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true)
      setError(null)
      const patientsData = await dataClient.getPatients(status)
      setPatients(patientsData)
    } catch (error) {
      console.error(`Error loading ${status} patients:`, error)
      setError(`Failed to load ${status} patients`)
    } finally {
      setIsLoading(false)
    }
  }, [status])

  useEffect(() => {
    loadPatients()
  }, [loadPatients])

  return {
    patients,
    isLoading,
    error,
    refetch: loadPatients,
    setPatients, // Allow manual updates for optimistic UI
  }
}

/**
 * Custom hook for loading a single patient by ID
 */
export function usePatient(patientId: string) {
  const [patient, setPatient] = useState<Patient | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadPatient = useCallback(async (): Promise<void> => {
    if (!patientId) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      const patientData = await dataClient.getPatient(patientId)
      setPatient(patientData || null)
    } catch (error) {
      console.error("Error loading patient:", error)
      setError("Failed to load patient")
    } finally {
      setIsLoading(false)
    }
  }, [patientId])

  useEffect(() => {
    loadPatient()
  }, [loadPatient])

  return {
    patient,
    isLoading,
    error,
    refetch: loadPatient,
    setPatient, // Allow manual updates
  }
}

/**
 * Custom hook for patient ID validation
 */
export function usePatientIdValidation() {
  const [isValidating, setIsValidating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const validatePatientId = useCallback(async (id: string): Promise<boolean> => {
    if (!id) {
      setError(null)
      return true
    }
    
    setIsValidating(true)
    setError(null)
    
    try {
      const existingPatient = await dataClient.getPatientByPatientId(id)
      if (existingPatient) {
        setError("Patient ID already exists. Please use a different ID.")
        return false
      }
      return true
    } catch (error) {
      console.error("Error validating patient ID:", error)
      setError("Failed to validate patient ID")
      return false
    } finally {
      setIsValidating(false)
    }
  }, [])

  return {
    validatePatientId,
    isValidating,
    error,
    clearError: () => setError(null),
  }
}
