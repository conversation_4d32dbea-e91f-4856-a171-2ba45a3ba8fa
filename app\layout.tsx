import type React from "react"
import type { Metada<PERSON>, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Toaster } from "@/components/ui/toaster"
import { ThemeProvider } from "@/components/theme-provider"
import { InstallPrompt } from "@/components/install-prompt"
import { PWADebugStatus } from "@/components/pwa-debug-status"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "ICU Patient Management",
  description: "Offline-first ICU patient management system",
  manifest: "/manifest.json",
    generator: 'Shaalan'
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#2563eb",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" forcedTheme="light" enableSystem={false} disableTransitionOnChange>
          {children}
          <Toaster />
          <InstallPrompt />
          <PWADebugStatus />
        </ThemeProvider>
        {/* Preload app shell and next assets to improve offline behavior */}
        <link rel="preload" href="/" as="document" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
