import type React from "react"
import type { Metada<PERSON>, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Toaster } from "@/components/ui/toaster"
import { ThemeProvider } from "@/components/theme-provider"
import { InstallPrompt } from "@/components/install-prompt"
import { PWADebugStatus } from "@/components/pwa-debug-status"
import { ServiceWorkerDebug } from "@/components/service-worker-debug"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "ICU Patient Management",
  description: "Offline-first ICU patient management system",
  manifest: "/manifest.json",
    generator: 'Shaalan'
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#2563eb",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" forcedTheme="light" enableSystem={false} disableTransitionOnChange>
          {children}
          <Toaster />
          <InstallPrompt />
          <PWADebugStatus />
          <ServiceWorkerDebug />
        </ThemeProvider>
        {/* Preload app shell and next assets to improve offline behavior */}
        <link rel="preload" href="/" as="document" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  console.log('[SW] Starting registration process...');
                  console.log('[SW] Current origin:', window.location.origin);
                  console.log('[SW] Current protocol:', window.location.protocol);

                  navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                  })
                    .then(function(registration) {
                      console.log('[SW] Registration successful:', registration);
                      console.log('[SW] Scope:', registration.scope);
                      console.log('[SW] Script URL:', registration.active?.scriptURL);

                      registration.addEventListener('updatefound', function() {
                        console.log('[SW] Update found');
                      });

                      if (registration.installing) {
                        console.log('[SW] Installing...');
                      }
                      if (registration.waiting) {
                        console.log('[SW] Waiting...');
                      }
                      if (registration.active) {
                        console.log('[SW] Active');
                      }
                    })
                    .catch(function(registrationError) {
                      console.error('[SW] Registration failed:', registrationError);
                      console.error('[SW] Error details:', registrationError.message);
                    });

                  // Listen for SW messages
                  navigator.serviceWorker.addEventListener('message', function(event) {
                    console.log('[SW] Message received:', event.data);
                  });
                });
              } else {
                console.warn('[SW] Service workers not supported');
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
