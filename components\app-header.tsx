"use client"

import { useState, useEffect } from "react"
import { Activity, Wifi, WifiOff, Users, LogOut } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useOnlineStatus } from "@/hooks/use-online-status"
import { useOutboxCount } from "@/hooks/use-outbox-count"
import { getSession, logout } from "@/lib/auth"
import type { Session } from "@/lib/auth"
import { useRouter } from "next/navigation"
import Link from "next/link"

export function AppHeader() {
  const isOnline = useOnlineStatus()
  const outboxCount = useOutboxCount()
  const router = useRouter()
  const [session, setSession] = useState<Session | null>(null)

  useEffect(() => {
    const currentSession = getSession()
    setSession(currentSession)
  }, [])

  const handleLogout = () => {
    logout()
    router.push("/login")
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <Activity className="h-6 w-6 text-blue-600" />
          <h1 className="text-lg font-semibold">ICU Patient Management</h1>
        </div>

        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            {isOnline ? <Wifi className="h-4 w-4 text-green-600" /> : <WifiOff className="h-4 w-4 text-red-600" />}
            <span className="text-sm text-muted-foreground">{isOnline ? "Online" : "Offline"}</span>
            {outboxCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {outboxCount} pending
              </Badge>
            )}
          </div>

          {/* Admin Link */}
          {session?.user.role === "admin" && (
            <Button variant="ghost" size="sm" asChild>
              <Link href="/admin">
                <Users className="h-4 w-4 mr-2" />
                Admin
              </Link>
            </Button>
          )}

          {/* Logout */}
          <Button variant="ghost" size="sm" onClick={handleLogout}>
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>
    </header>
  )
}
