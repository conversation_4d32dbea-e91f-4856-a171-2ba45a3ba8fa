# HTTPS Setup for Mobile PWA Development

## Method 1: Using mkcert (Recommended)

### Windows Installation
```bash
# Option 1: Using Chocolatey
choco install mkcert

# Option 2: Using Scoop
scoop bucket add extras
scoop install mkcert

# Option 3: Manual Download
# Download from: https://github.com/FiloSottile/mkcert/releases
# Add to PATH or place in project directory
```

### Setup Commands
```bash
# 1. Install local Certificate Authority
mkcert -install

# 2. Create certificates directory
mkdir certs

# 3. Find your local IP address
ipconfig | findstr "IPv4"

# 4. Generate certificates (replace ************* with your actual IP)
mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 127.0.0.1 ::1 ************ 192.168.1.* 10.0.0.* 172.16.*

# 5. Start HTTPS development server
npm run dev:https
```

## Method 2: Using OpenSSL (Alternative)

### Generate Self-Signed Certificate
```bash
# Create certs directory
mkdir certs

# Generate private key
openssl genrsa -out certs/localhost-key.pem 2048

# Create certificate signing request config
cat > certs/cert.conf << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=US
ST=State
L=City
O=Organization
CN=localhost

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = *************
IP.4 = ********
IP.5 = **********
EOF

# Generate certificate
openssl req -new -x509 -key certs/localhost-key.pem -out certs/localhost.pem -days 365 -config certs/cert.conf -extensions v3_req
```

## Testing Steps

### 1. Start HTTPS Server
```bash
npm run dev:https
```

### 2. Access from Desktop
```
https://localhost:3000
```

### 3. Access from Mobile
```
https://*************:3000
```
(Replace with your actual IP address)

### 4. Accept Security Warnings
- Chrome: Click "Advanced" → "Proceed to localhost (unsafe)"
- Mobile: Tap "Advanced" → "Proceed to [IP] (unsafe)"

## Troubleshooting

### Certificate Not Trusted
- Run `mkcert -install` to install the local CA
- Restart browsers after installation
- On mobile, you may need to accept security warnings

### Wrong IP Address
- Check your IP: `ipconfig | findstr "IPv4"`
- Regenerate certificates with correct IP
- Ensure mobile device is on same network

### Port Issues
- Ensure port 3000 is not blocked by firewall
- Try different port: `npm run dev:https -- -p 3001`

## Expected Results
✅ HTTPS/Secure Context: Secure
✅ Service Worker: Registered  
✅ Browser Support: Supported
✅ PWA Installable: Ready
✅ Install prompt appears on mobile browsers
