const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return '*************'; // fallback
}

// Create certificates directory
const certsDir = path.join(__dirname, '..', 'certs');
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir);
}

const keyPath = path.join(certsDir, 'localhost-key.pem');
const certPath = path.join(certsDir, 'localhost.pem');
const localIP = getLocalIP();

// Check if certificates exist
if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
  console.log('🔐 Generating HTTPS certificates for local development...');
  console.log(`📍 Detected local IP: ${localIP}`);
  console.log('');

  // Try mkcert first
  try {
    console.log('🔧 Attempting to use mkcert...');
    execSync('mkcert -version', { stdio: 'pipe' });

    console.log('✅ mkcert found! Generating certificates...');
    execSync(`mkcert -key-file "${keyPath}" -cert-file "${certPath}" localhost 127.0.0.1 ::1 ${localIP}`, {
      stdio: 'inherit'
    });

    console.log('✅ Certificates generated successfully with mkcert!');

  } catch (mkcertError) {
    console.log('⚠️  mkcert not found, trying OpenSSL...');

    try {
      console.log('📋 To install mkcert for easier setup:');
      console.log('   Windows: choco install mkcert');
      console.log('   Or download from: https://github.com/FiloSottile/mkcert/releases');
      console.log('');
      console.log('🔧 Generating self-signed certificate with OpenSSL...');
    
      // Fallback: Generate self-signed certificate with OpenSSL
      console.log('🔧 Attempting to generate self-signed certificate with OpenSSL...');
      
      const opensslConfig = `
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ${localIP}
IP.3 = ***********
IP.4 = *************
IP.5 = ***********01
IP.6 = ***********02
IP.7 = ********
IP.8 = **********
`;

      const configPath = path.join(certsDir, 'openssl.conf');
      fs.writeFileSync(configPath, opensslConfig);

      // Generate private key and certificate
      execSync(`openssl req -x509 -newkey rsa:2048 -keyout "${keyPath}" -out "${certPath}" -days 365 -nodes -config "${configPath}"`, {
        stdio: 'inherit'
      });

      console.log('✅ Self-signed certificate generated successfully!');
      console.log('⚠️  You will need to accept the security warning in your browser');
      
    } catch (error) {
      console.error('❌ Failed to generate certificate:', error.message);
      console.log('');
      console.log('🔧 Manual setup required:');
      console.log('1. Install mkcert: https://github.com/FiloSottile/mkcert');
      console.log('2. Run: mkcert -install');
      console.log('3. Run: mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 192.168.1.*');
      console.log('4. Then run: npm run dev:https');
      process.exit(1);
    }
  }
}

console.log('🚀 Starting HTTPS development server...');
console.log(`📱 Access from mobile: https://${localIP}:3000`);
console.log('💻 Access from desktop: https://localhost:3000');
console.log('⚠️  You may need to accept security warnings in browsers');
console.log('');

// Start custom HTTPS server
const serverPath = path.join(__dirname, '..', 'server-https.js');
const serverProcess = spawn('node', [serverPath], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: process.env.NODE_ENV || 'development'
  }
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start HTTPS server:', error.message);
  console.log('');
  console.log('🔧 Fallback: Try running manually:');
  console.log('   node server-https.js');
});

process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down HTTPS server...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});
