const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create certificates directory
const certsDir = path.join(__dirname, '..', 'certs');
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir);
}

const keyPath = path.join(certsDir, 'localhost-key.pem');
const certPath = path.join(certsDir, 'localhost.pem');

// Check if certificates exist
if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
  console.log('🔐 Generating HTTPS certificates for local development...');
  
  try {
    // Install mkcert if not available (you may need to install this manually)
    console.log('📋 To generate certificates, please install mkcert:');
    console.log('   Windows: choco install mkcert (or download from GitHub)');
    console.log('   macOS: brew install mkcert');
    console.log('   Linux: apt install mkcert (or equivalent)');
    console.log('');
    console.log('Then run: mkcert -install');
    console.log('Then run: mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 192.168.1.* 10.0.0.* 172.16.*');
    console.log('');
    console.log('Or use the manual OpenSSL method below...');
    
    // Fallback: Generate self-signed certificate with OpenSSL
    console.log('🔧 Attempting to generate self-signed certificate with OpenSSL...');
    
    const opensslConfig = `
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ***********
IP.3 = ***********00
IP.4 = ***********01
IP.5 = ***********02
IP.6 = ***********03
IP.7 = ***********04
IP.8 = ***********05
IP.9 = ********
IP.10 = **********
`;

    const configPath = path.join(certsDir, 'openssl.conf');
    fs.writeFileSync(configPath, opensslConfig);

    // Generate private key and certificate
    execSync(`openssl req -x509 -newkey rsa:2048 -keyout "${keyPath}" -out "${certPath}" -days 365 -nodes -config "${configPath}"`, {
      stdio: 'inherit'
    });

    console.log('✅ Self-signed certificate generated successfully!');
    console.log('⚠️  You will need to accept the security warning in your browser');
    
  } catch (error) {
    console.error('❌ Failed to generate certificate:', error.message);
    console.log('');
    console.log('🔧 Manual setup required:');
    console.log('1. Install mkcert: https://github.com/FiloSottile/mkcert');
    console.log('2. Run: mkcert -install');
    console.log('3. Run: mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 192.168.1.*');
    console.log('4. Then run: npm run dev:https');
    process.exit(1);
  }
}

console.log('🚀 Starting HTTPS development server...');
console.log('📱 Access from mobile: https://[your-ip]:3000');
console.log('💻 Access from desktop: https://localhost:3000');
console.log('');

// Start Next.js with HTTPS
process.env.HTTPS = 'true';
process.env.SSL_CRT = certPath;
process.env.SSL_KEY = keyPath;

require('child_process').spawn('next', ['dev', '--hostname', '0.0.0.0'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    HTTPS: 'true',
    SSL_CRT: certPath,
    SSL_KEY: keyPath
  }
});
