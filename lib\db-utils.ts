import { db } from "./db"

/**
 * Type-safe dynamic table access utility
 * Consolidates the pattern used in sync operations
 */
export async function bulkPutToTable(tableName: string, data: any[]): Promise<void> {
  if (!data || data.length === 0) {
    return
  }

  try {
    // Type-safe dynamic table access
    const table = db[tableName as keyof typeof db] as any
    
    if (!table || typeof table.bulkPut !== 'function') {
      throw new Error(`Invalid table: ${tableName}`)
    }
    
    await table.bulkPut(data)
  } catch (error) {
    console.error(`Error bulk putting data to ${tableName}:`, error)
    throw error
  }
}

/**
 * Type-safe dynamic table query utility
 */
export async function queryTable(tableName: string, query?: any): Promise<any[]> {
  try {
    const table = db[tableName as keyof typeof db] as any
    
    if (!table || typeof table.toArray !== 'function') {
      throw new Error(`Invalid table: ${tableName}`)
    }
    
    if (query) {
      return await table.where(query).toArray()
    }
    
    return await table.toArray()
  } catch (error) {
    console.error(`Error querying table ${tableName}:`, error)
    throw error
  }
}

/**
 * Utility for clearing a table
 */
export async function clearTable(tableName: string): Promise<void> {
  try {
    const table = db[tableName as keyof typeof db] as any
    
    if (!table || typeof table.clear !== 'function') {
      throw new Error(`Invalid table: ${tableName}`)
    }
    
    await table.clear()
  } catch (error) {
    console.error(`Error clearing table ${tableName}:`, error)
    throw error
  }
}

/**
 * Get table count utility
 */
export async function getTableCount(tableName: string): Promise<number> {
  try {
    const table = db[tableName as keyof typeof db] as any
    
    if (!table || typeof table.count !== 'function') {
      throw new Error(`Invalid table: ${tableName}`)
    }
    
    return await table.count()
  } catch (error) {
    console.error(`Error getting count for table ${tableName}:`, error)
    throw error
  }
}

/**
 * Constants for table names to ensure consistency
 */
export const TABLE_NAMES = {
  PATIENTS: "patients",
  VITAL_SIGNS: "vital_signs", 
  LAB_VALUES: "lab_values",
  MEDICATIONS: "medications",
  MEDICATION_NAMES: "medication_names",
  MEDICATION_ADHERENCE: "medication_adherence",
  DOCTOR_NOTES: "doctor_notes",
  CULTURES: "cultures",
  RADIOLOGY: "radiology",
  OUTBOX: "outbox",
  ID_MAPPINGS: "id_mappings",
} as const

/**
 * All sync table names for easy iteration
 */
export const SYNC_TABLE_NAMES = [
  TABLE_NAMES.PATIENTS,
  TABLE_NAMES.VITAL_SIGNS,
  TABLE_NAMES.LAB_VALUES,
  TABLE_NAMES.MEDICATIONS,
  TABLE_NAMES.MEDICATION_NAMES,
  TABLE_NAMES.MEDICATION_ADHERENCE,
  TABLE_NAMES.DOCTOR_NOTES,
  TABLE_NAMES.CULTURES,
  TABLE_NAMES.RADIOLOGY,
] as const

/**
 * Medication-specific table names
 */
export const MEDICATION_TABLE_NAMES = [
  TABLE_NAMES.MEDICATIONS,
  TABLE_NAMES.MEDICATION_NAMES,
  TABLE_NAMES.MEDICATION_ADHERENCE,
] as const
