# 📱 Mobile PWA Setup Guide

## 🎯 Goal
Enable PWA installation on mobile devices by setting up HTTPS for local network access.

## 🔍 Current Status
✅ **Desktop (localhost)**: PWA works perfectly  
❌ **Mobile (network IP)**: Requires HTTPS for PWA functionality

## 🚀 Quick Setup (3 Steps)

### Step 1: Generate SSL Certificates
```bash
# Stop current server first (Ctrl+C)

# Generate certificates automatically
npm run generate-certs
```

### Step 2: Start HTTPS Development Server
```bash
# Start HTTPS server
npm run dev:https
```

### Step 3: Test on Mobile
```bash
# Open on mobile browser:
https://[your-ip]:3000

# Example:
https://*************:3000
```

## 📋 Detailed Instructions

### Option A: Automatic Setup (Recommended)

1. **Stop current server** (Ctrl+C in terminal)

2. **Generate certificates**:
   ```bash
   npm run generate-certs
   ```
   
3. **Start HTTPS server**:
   ```bash
   npm run dev:https
   ```

4. **Test on desktop**:
   - Open: `https://localhost:3000`
   - Accept security warning if prompted

5. **Test on mobile**:
   - Find your IP address (shown in terminal output)
   - Open: `https://[your-ip]:3000`
   - Accept security warning: "Advanced" → "Proceed to site"

### Option B: Manual mkcert Setup

1. **Install mkcert**:
   ```bash
   # Windows (Chocolatey)
   choco install mkcert
   
   # Or download from:
   # https://github.com/FiloSottile/mkcert/releases
   ```

2. **Setup local CA**:
   ```bash
   mkcert -install
   ```

3. **Generate certificates**:
   ```bash
   mkdir certs
   mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 127.0.0.1 ::1 192.168.1.* 10.0.0.* 172.16.*
   ```

4. **Start HTTPS server**:
   ```bash
   npm run dev:https
   ```

## 🔧 Troubleshooting

### Certificate Generation Failed
```bash
# Check if mkcert is installed
mkcert -version

# If not installed, download from:
# https://github.com/FiloSottile/mkcert/releases

# Or try OpenSSL method (automatic in generate-certs script)
```

### Browser Security Warnings
- **Chrome**: Click "Advanced" → "Proceed to localhost (unsafe)"
- **Mobile Chrome**: Tap "Advanced" → "Proceed to [IP] (unsafe)"
- **This is normal** for self-signed certificates

### Wrong IP Address
```bash
# Find your IP address
ipconfig | findstr "IPv4"

# Regenerate certificates with correct IP
npm run generate-certs
```

### Port Already in Use
```bash
# Kill existing processes on port 3000
netstat -ano | findstr :3000
taskkill /PID [PID_NUMBER] /F

# Or use different port
npm run dev:https -- -p 3001
```

## ✅ Expected Results

After HTTPS setup, your **PWA Debug Status** should show:

- ✅ **HTTPS/Secure Context**: Secure
- ✅ **Manifest Available**: Found
- ✅ **Manifest Valid**: Valid
- ✅ **Service Worker**: Registered
- ✅ **Browser Support**: Supported
- ✅ **PWA Installable**: Ready

## 📱 Mobile Testing Checklist

1. **Access HTTPS URL** on mobile browser
2. **Accept security warnings** (normal for self-signed certs)
3. **Check PWA Debug Status** (click "PWA Debug" button)
4. **Wait 30+ seconds** for install prompt to appear
5. **Look for install prompt** or "Add to Home Screen" option

## 🎉 Success Indicators

- **Install prompt appears** on mobile browsers
- **Console shows**: "beforeinstallprompt event fired!"
- **PWA Debug Status**: All green checkmarks
- **App can be installed** and works offline

## 🆘 Still Having Issues?

1. **Check PWA Debug Status** - shows exactly what's missing
2. **Look at browser console** for error messages
3. **Verify certificates exist** in `certs/` directory
4. **Ensure mobile device** is on same network
5. **Try different mobile browser** (Chrome, Brave, Edge)

## 📞 Common Solutions

| Issue | Solution |
|-------|----------|
| "Not Secure" on mobile | Use HTTPS setup above |
| Certificate errors | Accept browser security warnings |
| Install prompt not showing | Wait 30+ seconds, check PWA Debug Status |
| Wrong IP address | Run `ipconfig`, regenerate certificates |
| Port blocked | Check firewall, try different port |

## 🔄 Reset Everything

If you need to start fresh:

```bash
# Remove certificates
rmdir /s certs

# Regenerate everything
npm run generate-certs
npm run dev:https
```

Your ICU patient management app will then be installable on mobile devices! 🎯
