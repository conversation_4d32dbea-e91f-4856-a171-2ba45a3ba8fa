const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return '*************'; // fallback
}

const localIP = getLocalIP();
const certsDir = path.join(__dirname, '..', 'certs');
const keyPath = path.join(certsDir, 'localhost-key.pem');
const certPath = path.join(certsDir, 'localhost.pem');

console.log('🔐 SSL Certificate Generator for PWA Development');
console.log(`📍 Detected local IP: ${localIP}`);
console.log('');

// Create certs directory
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir);
  console.log('📁 Created certs directory');
}

// Method 1: Try mkcert
console.log('🔧 Method 1: Trying mkcert (recommended)...');
try {
  execSync('mkcert -version', { stdio: 'pipe' });
  console.log('✅ mkcert found!');
  
  // Check if CA is installed
  try {
    console.log('🔒 Installing local Certificate Authority...');
    execSync('mkcert -install', { stdio: 'inherit' });
  } catch (e) {
    console.log('⚠️  CA installation may have failed, continuing...');
  }
  
  console.log('🔑 Generating certificates...');
  // Use specific IP ranges instead of wildcards for mkcert compatibility
  const mkcertCommand = `mkcert -key-file "${keyPath}" -cert-file "${certPath}" localhost 127.0.0.1 ::1 ${localIP}`;
  execSync(mkcertCommand, { stdio: 'inherit' });
  
  console.log('✅ Certificates generated successfully with mkcert!');
  console.log('');
  console.log('🚀 Next steps:');
  console.log('   1. Run: npm run dev:https');
  console.log(`   2. Open: https://${localIP}:3000 on your mobile device`);
  console.log('   3. Accept security warnings if prompted');
  
} catch (error) {
  console.log('❌ mkcert not found or failed');
  console.log('');
  console.log('📋 To install mkcert:');
  console.log('   Windows: choco install mkcert');
  console.log('   Or download: https://github.com/FiloSottile/mkcert/releases');
  console.log('');
  
  // Method 2: OpenSSL fallback
  console.log('🔧 Method 2: Trying OpenSSL fallback...');
  try {
    execSync('openssl version', { stdio: 'pipe' });
    console.log('✅ OpenSSL found!');
    
    // Create OpenSSL config
    const opensslConfig = `[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=US
ST=State
L=City
O=ICU Management
CN=localhost

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = ${localIP}
IP.4 = ***********
IP.5 = *************
IP.6 = ********
IP.7 = **********`;

    const configPath = path.join(certsDir, 'openssl.conf');
    fs.writeFileSync(configPath, opensslConfig);
    
    console.log('🔑 Generating self-signed certificate...');
    execSync(`openssl req -new -x509 -key-out "${keyPath}" -out "${certPath}" -days 365 -config "${configPath}" -extensions v3_req -newkey rsa:2048 -nodes`, {
      stdio: 'inherit'
    });
    
    console.log('✅ Self-signed certificate generated!');
    console.log('⚠️  Browsers will show security warnings (this is normal)');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   1. Run: npm run dev:https');
    console.log(`   2. Open: https://${localIP}:3000 on your mobile device`);
    console.log('   3. Click "Advanced" → "Proceed to site" in browser warnings');
    
  } catch (opensslError) {
    console.log('❌ OpenSSL not found or failed');
    console.log('');
    console.log('🆘 Manual setup required:');
    console.log('   1. Install mkcert: https://github.com/FiloSottile/mkcert');
    console.log('   2. Or install OpenSSL for Windows');
    console.log('   3. Then run this script again');
    process.exit(1);
  }
}

// Verify certificates were created
if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
  console.log('');
  console.log('✅ Certificate files created:');
  console.log(`   🔑 Private key: ${keyPath}`);
  console.log(`   📜 Certificate: ${certPath}`);
  console.log('');
  console.log('🎯 Ready for HTTPS development!');
} else {
  console.log('');
  console.log('❌ Certificate generation failed');
  console.log('Please check the error messages above');
}
