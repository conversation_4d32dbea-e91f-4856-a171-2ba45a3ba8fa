"use client"

import { useState, useEffect } from "react"
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface ServiceWorkerStatus {
  isSupported: boolean
  isRegistered: boolean
  isActive: boolean
  scope: string | null
  scriptURL: string | null
  state: string | null
  updateFound: boolean
  error: string | null
  swFileAccessible: boolean
  registrationAttempted: boolean
}

export function ServiceWorkerDebug() {
  const [status, setStatus] = useState<ServiceWorkerStatus>({
    isSupported: false,
    isRegistered: false,
    isActive: false,
    scope: null,
    scriptURL: null,
    state: null,
    updateFound: false,
    error: null,
    swFileAccessible: false,
    registrationAttempted: false
  })
  const [logs, setLogs] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`])
    console.log(`[SW Debug] ${message}`)
  }

  const checkServiceWorkerStatus = async () => {
    addLog('Starting service worker status check...')
    
    const newStatus: ServiceWorkerStatus = {
      isSupported: 'serviceWorker' in navigator,
      isRegistered: false,
      isActive: false,
      scope: null,
      scriptURL: null,
      state: null,
      updateFound: false,
      error: null,
      swFileAccessible: false,
      registrationAttempted: false
    }

    if (!newStatus.isSupported) {
      addLog('❌ Service Worker not supported in this browser')
      setStatus(newStatus)
      return
    }

    addLog('✅ Service Worker supported')

    // Check if sw.js file is accessible
    try {
      const swResponse = await fetch('/sw.js', { method: 'HEAD' })
      newStatus.swFileAccessible = swResponse.ok
      addLog(`SW file accessible: ${swResponse.ok} (status: ${swResponse.status})`)
    } catch (error) {
      addLog(`❌ SW file check failed: ${error}`)
      newStatus.error = `SW file not accessible: ${error}`
    }

    // Check current registration
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        newStatus.isRegistered = true
        newStatus.scope = registration.scope
        newStatus.scriptURL = registration.active?.scriptURL || null
        newStatus.state = registration.active?.state || null
        newStatus.isActive = registration.active?.state === 'activated'
        
        addLog(`✅ SW registered - Scope: ${registration.scope}`)
        addLog(`SW Script URL: ${registration.active?.scriptURL}`)
        addLog(`SW State: ${registration.active?.state}`)
      } else {
        addLog('❌ No service worker registration found')
      }
    } catch (error) {
      addLog(`❌ Registration check failed: ${error}`)
      newStatus.error = `Registration check failed: ${error}`
    }

    setStatus(newStatus)
  }

  const forceRegisterServiceWorker = async () => {
    addLog('🔄 Attempting to force register service worker...')
    
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })
      
      addLog(`✅ Registration successful - Scope: ${registration.scope}`)
      
      registration.addEventListener('updatefound', () => {
        addLog('🔄 Service worker update found')
        setStatus(prev => ({ ...prev, updateFound: true }))
      })

      if (registration.installing) {
        addLog('⏳ Service worker installing...')
        registration.installing.addEventListener('statechange', (e) => {
          const sw = e.target as ServiceWorker
          addLog(`SW state changed to: ${sw.state}`)
        })
      }

      if (registration.waiting) {
        addLog('⏳ Service worker waiting...')
      }

      if (registration.active) {
        addLog('✅ Service worker active')
      }

      // Refresh status after registration
      setTimeout(checkServiceWorkerStatus, 1000)
      
    } catch (error) {
      addLog(`❌ Registration failed: ${error}`)
      setStatus(prev => ({ 
        ...prev, 
        error: `Registration failed: ${error}`,
        registrationAttempted: true 
      }))
    }
  }

  const unregisterServiceWorker = async () => {
    addLog('🗑️ Unregistering service worker...')
    
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        await registration.unregister()
        addLog('✅ Service worker unregistered')
      } else {
        addLog('ℹ️ No service worker to unregister')
      }
      
      // Clear caches
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
      addLog('✅ Caches cleared')
      
      setTimeout(checkServiceWorkerStatus, 1000)
      
    } catch (error) {
      addLog(`❌ Unregistration failed: ${error}`)
    }
  }

  useEffect(() => {
    if (isVisible) {
      checkServiceWorkerStatus()
    }
  }, [isVisible])

  const StatusIcon = ({ condition }: { condition: boolean }) => {
    return condition ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="outline"
        size="sm"
        className="fixed bottom-4 left-4 z-50"
      >
        SW Debug
      </Button>
    )
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96 max-h-[80vh] overflow-y-auto">
      <Card className="shadow-lg border-2 border-orange-200 bg-white">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 text-orange-600" />
              <CardTitle className="text-lg">Service Worker Debug</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
          <CardDescription>
            Debug service worker registration issues
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {/* Status Checks */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <StatusIcon condition={status.isSupported} />
                <span className="text-sm">Browser Support</span>
              </div>
              <Badge variant={status.isSupported ? "default" : "destructive"}>
                {status.isSupported ? "Supported" : "Not Supported"}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <StatusIcon condition={status.swFileAccessible} />
                <span className="text-sm">SW File Accessible</span>
              </div>
              <Badge variant={status.swFileAccessible ? "default" : "destructive"}>
                {status.swFileAccessible ? "Accessible" : "Not Found"}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <StatusIcon condition={status.isRegistered} />
                <span className="text-sm">SW Registered</span>
              </div>
              <Badge variant={status.isRegistered ? "default" : "destructive"}>
                {status.isRegistered ? "Registered" : "Not Registered"}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <StatusIcon condition={status.isActive} />
                <span className="text-sm">SW Active</span>
              </div>
              <Badge variant={status.isActive ? "default" : "destructive"}>
                {status.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          {/* Details */}
          {status.isRegistered && (
            <div className="text-xs text-gray-600 space-y-1 border-t pt-2">
              <div>Scope: {status.scope}</div>
              <div>State: {status.state}</div>
              <div>Script: {status.scriptURL?.split('/').pop()}</div>
            </div>
          )}

          {/* Error Display */}
          {status.error && (
            <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
              {status.error}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={checkServiceWorkerStatus} 
              variant="outline" 
              size="sm"
              className="flex-1"
            >
              Refresh
            </Button>
            <Button 
              onClick={forceRegisterServiceWorker} 
              variant="default" 
              size="sm"
              className="flex-1"
            >
              Register
            </Button>
            <Button 
              onClick={unregisterServiceWorker} 
              variant="destructive" 
              size="sm"
              className="flex-1"
            >
              Reset
            </Button>
          </div>

          {/* Logs */}
          <div className="border-t pt-2">
            <div className="text-xs font-medium mb-1">Debug Log:</div>
            <div className="text-xs text-gray-600 space-y-1 max-h-32 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
              {logs.length === 0 && <div className="text-gray-400">No logs yet...</div>}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
