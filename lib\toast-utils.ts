import { toast } from "@/hooks/use-toast"

/**
 * Standardized toast utilities for common success/error scenarios
 * Consolidates toast usage patterns across the application
 */

interface ToastOptions {
  title?: string
  description?: string
  isOnline?: boolean
}

/**
 * Success toast for data operations
 */
export const toastSuccess = {
  /**
   * Patient-related success messages
   */
  patient: {
    added: (options?: ToastOptions) => toast({
      title: options?.title || "Patient added",
      description: options?.description || "Patient has been successfully added to the system.",
    }),
    
    updated: (options?: ToastOptions) => toast({
      title: options?.title || "Patient updated", 
      description: options?.description || "Patient information has been successfully updated.",
    }),
    
    discharged: (options?: ToastOptions) => toast({
      title: options?.title || "Patient discharged",
      description: options?.description || "Patient has been successfully discharged.",
    }),
    
    deceased: (options?: ToastOptions) => toast({
      title: options?.title || "Patient status updated",
      description: options?.description || "Patient status has been updated to deceased.",
    }),
  },

  /**
   * Medical data success messages
   */
  medical: {
    vitalsAdded: (options?: ToastOptions) => toast({
      title: options?.title || "Vital signs added",
      description: options?.description || "Vital signs have been successfully recorded.",
    }),
    
    labsAdded: (options?: ToastOptions) => toast({
      title: options?.title || "Lab values added",
      description: options?.description || "Lab values have been successfully recorded.",
    }),
    
    medicationAdded: (options?: ToastOptions) => toast({
      title: options?.title || "Medication added",
      description: options?.description || "Medication has been successfully added.",
    }),
    
    adherenceUpdated: (date: string, options?: ToastOptions) => toast({
      title: options?.title || "Updated",
      description: options?.description || (
        options?.isOnline 
          ? `Medication adherence updated for ${date}`
          : `Medication adherence updated for ${date} (will sync when online)`
      ),
    }),
    
    noteAdded: (options?: ToastOptions) => toast({
      title: options?.title || "Note added",
      description: options?.description || "Doctor note has been successfully added.",
    }),
    
    cultureAdded: (options?: ToastOptions) => toast({
      title: options?.title || "Culture added",
      description: options?.description || "Culture has been successfully added.",
    }),
    
    radiologyAdded: (options?: ToastOptions) => toast({
      title: options?.title || "Radiology added",
      description: options?.description || "Radiology record has been successfully added.",
    }),
  },

  /**
   * Generic success message
   */
  generic: (title: string, description?: string) => toast({
    title,
    description,
  }),
}

/**
 * Error toast for common failure scenarios
 */
export const toastError = {
  /**
   * Patient-related error messages
   */
  patient: {
    loadFailed: (options?: ToastOptions) => toast({
      title: options?.title || "Error",
      description: options?.description || "Failed to load patient data. Please try again.",
      variant: "destructive",
    }),
    
    addFailed: (options?: ToastOptions) => toast({
      title: options?.title || "Error",
      description: options?.description || "Failed to add patient. Please try again.",
      variant: "destructive",
    }),
    
    updateFailed: (options?: ToastOptions) => toast({
      title: options?.title || "Error", 
      description: options?.description || "Failed to update patient. Please try again.",
      variant: "destructive",
    }),
    
    idExists: (options?: ToastOptions) => toast({
      title: options?.title || "Validation Error",
      description: options?.description || "Please resolve the patient ID error before submitting.",
      variant: "destructive",
    }),
  },

  /**
   * Medical data error messages
   */
  medical: {
    saveFailed: (type: string, options?: ToastOptions) => toast({
      title: options?.title || "Error",
      description: options?.description || `Failed to save ${type}. Please try again.`,
      variant: "destructive",
    }),
    
    loadFailed: (type: string, options?: ToastOptions) => toast({
      title: options?.title || "Error",
      description: options?.description || `Failed to load ${type}. Please try again.`,
      variant: "destructive",
    }),
    
    adherenceFailed: (options?: ToastOptions) => toast({
      title: options?.title || "Error",
      description: options?.description || "Failed to save medication adherence. Please try again.",
      variant: "destructive",
    }),
  },

  /**
   * Network and sync errors
   */
  network: {
    offline: (options?: ToastOptions) => toast({
      title: options?.title || "Offline",
      description: options?.description || "You are currently offline. Changes will sync when connection is restored.",
      variant: "destructive",
    }),
    
    syncFailed: (options?: ToastOptions) => toast({
      title: options?.title || "Sync Error",
      description: options?.description || "Failed to sync data. Please check your connection.",
      variant: "destructive",
    }),
  },

  /**
   * Generic error message
   */
  generic: (title: string, description?: string) => toast({
    title,
    description,
    variant: "destructive",
  }),
}

/**
 * Utility function to format dates for toast messages
 */
export function formatDateForToast(dateString: string): string {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}
